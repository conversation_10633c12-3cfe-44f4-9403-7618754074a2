/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* الرأس والتنقل */
header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 1rem 2rem;
}

header h1 {
    text-align: center;
    margin-bottom: 1rem;
    font-size: 2rem;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

nav a:hover,
nav a.active {
    background-color: rgba(255,255,255,0.2);
}

/* المحتوى الرئيسي */
main {
    padding: 2rem;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

/* لوحة التحكم */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.card {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.card h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.card .count {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.recent-activities {
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 1.5rem;
}

.recent-activities h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.recent-activities ul {
    list-style: none;
}

.recent-activities li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.recent-activities li:last-child {
    border-bottom: none;
}

.dashboard-actions {
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.dashboard-actions h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

/* رأس الأقسام */
.section-header {
    margin-bottom: 1.5rem;
    text-align: right;
}

/* النماذج */
.form-container {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-container h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    margin-top: 1.5rem;
    text-align: right;
    gap: 1rem;
    display: flex;
    justify-content: flex-end;
}

/* الجداول */
.table-container {
    overflow-x: auto;
    border: 1px solid #ddd;
    border-radius: 10px;
    background: white;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #eee;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
    position: sticky;
    top: 0;
}

table tr:hover {
    background-color: #f8f9fa;
}

table tr:last-child td {
    border-bottom: none;
}

/* الإجراءات في الجداول */
.table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.table-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    header {
        padding: 1rem;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    nav ul {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    main {
        padding: 1rem;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .table-container {
        font-size: 0.875rem;
    }
    
    table th,
    table td {
        padding: 0.5rem;
    }
}

/* حالات التحميل */
.loading {
    text-align: center;
    padding: 2rem;
    color: #7f8c8d;
}

.loading::after {
    content: "...";
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(0,0,0,0);
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    40% {
        color: #7f8c8d;
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    60% {
        text-shadow:
            .25em 0 0 #7f8c8d,
            .5em 0 0 rgba(0,0,0,0);
    }
    80%, 100% {
        text-shadow:
            .25em 0 0 #7f8c8d,
            .5em 0 0 #7f8c8d;
    }
}

/* رسائل النجاح والخطأ */
.message {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.empty-state h3 {
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* شارات الحالة */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: bold;
    text-align: center;
    display: inline-block;
}

.status-مخطط {
    background-color: #e3f2fd;
    color: #1976d2;
}

.status-قيد-التنفيذ {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-مكتمل {
    background-color: #e8f5e8;
    color: #388e3c;
}

.status-معلق {
    background-color: #fce4ec;
    color: #c2185b;
}

.status-ملغي {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

/* أزرار إضافية */
.btn-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background-color: #3498db;
    color: white;
}

/* تحسينات للجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-striped tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: #e9ecef;
}

/* مؤشرات التقدم */
.progress {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

/* بطاقات إضافية */
.info-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.info-card .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.info-card .info-label {
    font-weight: bold;
    color: #7f8c8d;
}

.info-card .info-value {
    color: #2c3e50;
}

/* تحسينات للنماذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-row .form-group {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* أيقونات */
.icon {
    width: 16px;
    height: 16px;
    margin-left: 0.5rem;
    vertical-align: middle;
}

/* تنسيقات التقارير */
.report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.report-table th,
.report-table td {
    border: 1px solid #ddd;
    padding: 0.75rem;
    text-align: right;
}

.report-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

.report-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* تحسينات للطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    .container {
        box-shadow: none;
        max-width: none;
    }

    header {
        background: white !important;
        color: black !important;
    }

    .btn {
        display: none;
    }

    table {
        font-size: 12px;
    }

    .section {
        display: block !important;
    }

    .report-table {
        page-break-inside: avoid;
    }

    .report-table th {
        background-color: #f0f0f0 !important;
    }
}

/* تحسينات إضافية */
.section-divider {
    border-top: 2px solid #3498db;
    margin: 2rem 0;
    padding-top: 1rem;
}

.highlight-box {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left: 4px solid #2196f3;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.warning-box {
    background: linear-gradient(135deg, #fff3e0, #ffcc02);
    border-left: 4px solid #ff9800;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.error-box {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    border-left: 4px solid #f44336;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.success-box {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    border-left: 4px solid #4caf50;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

/* تحسينات للشارات */
.status-عالي {
    background-color: #ffebee;
    color: #c62828;
}

.status-متوسط {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-منخفض {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-عالي-جداً {
    background-color: #f3e5f5;
    color: #6a1b9a;
    font-weight: bold;
}

.status-منخفض-جداً {
    background-color: #e0f2f1;
    color: #00695c;
}

.status-مفتوح {
    background-color: #ffebee;
    color: #c62828;
}

.status-مغلق {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-نشط {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-مخفف {
    background-color: #e8f5e8;
    color: #2e7d32;
}

/* تحسينات للنماذج المتقدمة */
.form-section {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: #fafafa;
}

.form-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.5rem;
}

.required-field::after {
    content: " *";
    color: #e74c3c;
}

/* تحسينات للتفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.clickable:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* تحسينات للتحميل */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
