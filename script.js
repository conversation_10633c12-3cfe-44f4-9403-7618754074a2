// متغيرات عامة
let db = null;
let SQL = null;

// تهيئة قاعدة البيانات
async function initDatabase() {
    try {
        // تحميل sql.js
        const sqlPromise = initSqlJs({
            locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
        });
        SQL = await sqlPromise;
        
        // إنشاء قاعدة بيانات جديدة
        db = new SQL.Database();
        
        // إنشاء الجداول
        createTables();
        
        // تحديث لوحة التحكم
        updateDashboard();
        
        console.log('تم تهيئة قاعدة البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
        showMessage('خطأ في تهيئة قاعدة البيانات', 'error');
    }
}

// إنشاء الجداول
function createTables() {
    const tables = [
        // جدول العملاء
        `CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            industry TEXT,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        
        // جدول المراجعين
        `CREATE TABLE IF NOT EXISTS auditors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            position TEXT NOT NULL,
            license_number TEXT,
            specialization TEXT,
            experience_years INTEGER,
            email TEXT,
            phone TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        
        // جدول خطط المراجعة
        `CREATE TABLE IF NOT EXISTS audit_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            financial_year INTEGER NOT NULL,
            audit_type TEXT NOT NULL,
            lead_auditor_id INTEGER NOT NULL,
            start_date DATE,
            end_date DATE,
            materiality_level DECIMAL(15,2),
            objectives TEXT,
            scope TEXT,
            status TEXT DEFAULT 'مخطط',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id),
            FOREIGN KEY (lead_auditor_id) REFERENCES auditors(id)
        )`,
        
        // جدول إجراءات المراجعة
        `CREATE TABLE IF NOT EXISTS audit_procedures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            audit_plan_id INTEGER NOT NULL,
            procedure_name TEXT NOT NULL,
            description TEXT,
            assigned_auditor_id INTEGER,
            status TEXT DEFAULT 'لم يبدأ',
            start_date DATE,
            completion_date DATE,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (audit_plan_id) REFERENCES audit_plans(id),
            FOREIGN KEY (assigned_auditor_id) REFERENCES auditors(id)
        )`,
        
        // جدول النتائج والملاحظات
        `CREATE TABLE IF NOT EXISTS audit_findings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            audit_plan_id INTEGER NOT NULL,
            procedure_id INTEGER,
            finding_type TEXT NOT NULL,
            severity TEXT NOT NULL,
            description TEXT NOT NULL,
            recommendation TEXT,
            management_response TEXT,
            status TEXT DEFAULT 'مفتوح',
            due_date DATE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (audit_plan_id) REFERENCES audit_plans(id),
            FOREIGN KEY (procedure_id) REFERENCES audit_procedures(id)
        )`,
        
        // جدول المخاطر
        `CREATE TABLE IF NOT EXISTS risks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            risk_category TEXT NOT NULL,
            risk_description TEXT NOT NULL,
            likelihood TEXT NOT NULL,
            impact TEXT NOT NULL,
            risk_level TEXT NOT NULL,
            mitigation_strategy TEXT,
            status TEXT DEFAULT 'نشط',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id)
        )`
    ];
    
    tables.forEach(tableSQL => {
        try {
            db.run(tableSQL);
        } catch (error) {
            console.error('خطأ في إنشاء الجدول:', error);
        }
    });
}

// التنقل بين الأقسام
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // إزالة الفئة النشطة من جميع روابط التنقل
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // إظهار القسم المطلوب
    document.getElementById(sectionId).classList.add('active');
    
    // إضافة الفئة النشطة للرابط المناسب
    document.querySelector(`[href="#${sectionId}"]`).classList.add('active');
    
    // تحديث البيانات حسب القسم
    switch(sectionId) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'clients':
            loadClients();
            break;
        case 'auditors':
            loadAuditors();
            break;
        case 'planning':
            loadAuditPlans();
            loadClientsForSelect();
            loadAuditorsForSelect();
            break;
        case 'procedures':
            loadProcedures();
            break;
        case 'findings':
            loadFindings();
            break;
        case 'risks':
            loadRisks();
            break;
    }
}

// تحديث لوحة التحكم
function updateDashboard() {
    if (!db) return;
    
    try {
        // إجمالي العملاء
        const clientsResult = db.exec("SELECT COUNT(*) as count FROM clients");
        const totalClients = clientsResult.length > 0 ? clientsResult[0].values[0][0] : 0;
        document.getElementById('total-clients').textContent = totalClients;
        
        // المراجعات النشطة
        const activeAuditsResult = db.exec("SELECT COUNT(*) as count FROM audit_plans WHERE status IN ('مخطط', 'قيد التنفيذ')");
        const activeAudits = activeAuditsResult.length > 0 ? activeAuditsResult[0].values[0][0] : 0;
        document.getElementById('active-audits').textContent = activeAudits;
        
        // المراجعات المكتملة
        const completedAuditsResult = db.exec("SELECT COUNT(*) as count FROM audit_plans WHERE status = 'مكتمل'");
        const completedAudits = completedAuditsResult.length > 0 ? completedAuditsResult[0].values[0][0] : 0;
        document.getElementById('completed-audits').textContent = completedAudits;
        
        // الملاحظات المعلقة
        const pendingFindingsResult = db.exec("SELECT COUNT(*) as count FROM audit_findings WHERE status = 'مفتوح'");
        const pendingFindings = pendingFindingsResult.length > 0 ? pendingFindingsResult[0].values[0][0] : 0;
        document.getElementById('pending-findings').textContent = pendingFindings;
        
    } catch (error) {
        console.error('خطأ في تحديث لوحة التحكم:', error);
    }
}

// إدارة العملاء
function showAddClientForm() {
    document.getElementById('add-client-form').style.display = 'block';
}

function hideAddClientForm() {
    document.getElementById('add-client-form').style.display = 'none';
    document.querySelector('#add-client-form form').reset();
}

function addClient(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('client-name').value,
        type: document.getElementById('client-type').value,
        industry: document.getElementById('client-industry').value,
        contact_person: document.getElementById('client-contact').value,
        phone: document.getElementById('client-phone').value,
        email: document.getElementById('client-email').value,
        address: document.getElementById('client-address').value
    };
    
    try {
        const stmt = db.prepare(`
            INSERT INTO clients (name, type, industry, contact_person, phone, email, address)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            formData.name,
            formData.type,
            formData.industry,
            formData.contact_person,
            formData.phone,
            formData.email,
            formData.address
        ]);
        
        stmt.free();
        
        showMessage('تم إضافة العميل بنجاح', 'success');
        hideAddClientForm();
        loadClients();
        updateDashboard();
        
    } catch (error) {
        console.error('خطأ في إضافة العميل:', error);
        showMessage('خطأ في إضافة العميل', 'error');
    }
}

function loadClients() {
    if (!db) return;
    
    try {
        const result = db.exec("SELECT * FROM clients ORDER BY created_at DESC");
        const tbody = document.getElementById('clients-tbody');
        tbody.innerHTML = '';
        
        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td>${row[2]}</td>
                    <td>${row[3] || '-'}</td>
                    <td>${row[4] || '-'}</td>
                    <td>${row[5] || '-'}</td>
                    <td class="table-actions">
                        <button class="btn btn-primary" onclick="editClient(${row[0]})">تعديل</button>
                        <button class="btn btn-danger" onclick="deleteClient(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="6" class="empty-state">لا توجد عملاء مسجلين</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
    }
}

function deleteClient(clientId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        try {
            db.run("DELETE FROM clients WHERE id = ?", [clientId]);
            showMessage('تم حذف العميل بنجاح', 'success');
            loadClients();
            updateDashboard();
        } catch (error) {
            console.error('خطأ في حذف العميل:', error);
            showMessage('خطأ في حذف العميل', 'error');
        }
    }
}

// إدارة المراجعين
function showAddAuditorForm() {
    document.getElementById('add-auditor-form').style.display = 'block';
}

function hideAddAuditorForm() {
    document.getElementById('add-auditor-form').style.display = 'none';
    document.querySelector('#add-auditor-form form').reset();
}

function addAuditor(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('auditor-name').value,
        position: document.getElementById('auditor-position').value,
        license_number: document.getElementById('auditor-license').value,
        specialization: document.getElementById('auditor-specialization').value,
        experience_years: document.getElementById('auditor-experience').value,
        email: document.getElementById('auditor-email').value,
        phone: document.getElementById('auditor-phone').value
    };
    
    try {
        const stmt = db.prepare(`
            INSERT INTO auditors (name, position, license_number, specialization, experience_years, email, phone)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            formData.name,
            formData.position,
            formData.license_number,
            formData.specialization,
            formData.experience_years,
            formData.email,
            formData.phone
        ]);
        
        stmt.free();
        
        showMessage('تم إضافة المراجع بنجاح', 'success');
        hideAddAuditorForm();
        loadAuditors();
        
    } catch (error) {
        console.error('خطأ في إضافة المراجع:', error);
        showMessage('خطأ في إضافة المراجع', 'error');
    }
}

function loadAuditors() {
    if (!db) return;
    
    try {
        const result = db.exec("SELECT * FROM auditors ORDER BY created_at DESC");
        const tbody = document.getElementById('auditors-tbody');
        tbody.innerHTML = '';
        
        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td>${row[2]}</td>
                    <td>${row[3] || '-'}</td>
                    <td>${row[4] || '-'}</td>
                    <td>${row[5] || '-'}</td>
                    <td class="table-actions">
                        <button class="btn btn-primary" onclick="editAuditor(${row[0]})">تعديل</button>
                        <button class="btn btn-danger" onclick="deleteAuditor(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="6" class="empty-state">لا توجد مراجعين مسجلين</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل المراجعين:', error);
    }
}

function deleteAuditor(auditorId) {
    if (confirm('هل أنت متأكد من حذف هذا المراجع؟')) {
        try {
            db.run("DELETE FROM auditors WHERE id = ?", [auditorId]);
            showMessage('تم حذف المراجع بنجاح', 'success');
            loadAuditors();
        } catch (error) {
            console.error('خطأ في حذف المراجع:', error);
            showMessage('خطأ في حذف المراجع', 'error');
        }
    }
}

// عرض الرسائل
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // إدراج الرسالة في أعلى المحتوى
    const main = document.querySelector('main');
    main.insertBefore(messageDiv, main.firstChild);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// تخطيط المراجعة
function showAddAuditPlanForm() {
    document.getElementById('add-audit-plan-form').style.display = 'block';
}

function hideAddAuditPlanForm() {
    document.getElementById('add-audit-plan-form').style.display = 'none';
    document.querySelector('#add-audit-plan-form form').reset();
}

function loadClientsForSelect() {
    if (!db) return;

    try {
        const result = db.exec("SELECT id, name FROM clients ORDER BY name");
        const select = document.getElementById('plan-client');

        // مسح الخيارات الحالية (عدا الخيار الافتراضي)
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const option = document.createElement('option');
                option.value = row[0];
                option.textContent = row[1];
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
    }
}

function loadAuditorsForSelect() {
    if (!db) return;

    try {
        const result = db.exec("SELECT id, name, position FROM auditors ORDER BY name");
        const select = document.getElementById('plan-lead-auditor');

        // مسح الخيارات الحالية (عدا الخيار الافتراضي)
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const option = document.createElement('option');
                option.value = row[0];
                option.textContent = `${row[1]} - ${row[2]}`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل المراجعين:', error);
    }
}

function addAuditPlan(event) {
    event.preventDefault();

    const formData = {
        client_id: document.getElementById('plan-client').value,
        financial_year: document.getElementById('plan-year').value,
        audit_type: document.getElementById('plan-type').value,
        lead_auditor_id: document.getElementById('plan-lead-auditor').value,
        start_date: document.getElementById('plan-start-date').value,
        end_date: document.getElementById('plan-end-date').value,
        materiality_level: document.getElementById('plan-materiality').value,
        objectives: document.getElementById('plan-objectives').value,
        scope: document.getElementById('plan-scope').value
    };

    try {
        const stmt = db.prepare(`
            INSERT INTO audit_plans (client_id, financial_year, audit_type, lead_auditor_id,
                                   start_date, end_date, materiality_level, objectives, scope)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run([
            formData.client_id,
            formData.financial_year,
            formData.audit_type,
            formData.lead_auditor_id,
            formData.start_date,
            formData.end_date,
            formData.materiality_level,
            formData.objectives,
            formData.scope
        ]);

        stmt.free();

        showMessage('تم إنشاء خطة المراجعة بنجاح', 'success');
        hideAddAuditPlanForm();
        loadAuditPlans();
        updateDashboard();

    } catch (error) {
        console.error('خطأ في إنشاء خطة المراجعة:', error);
        showMessage('خطأ في إنشاء خطة المراجعة', 'error');
    }
}

function loadAuditPlans() {
    if (!db) return;

    try {
        const result = db.exec(`
            SELECT ap.id, c.name as client_name, ap.financial_year, ap.audit_type,
                   a.name as auditor_name, ap.start_date, ap.status
            FROM audit_plans ap
            JOIN clients c ON ap.client_id = c.id
            JOIN auditors a ON ap.lead_auditor_id = a.id
            ORDER BY ap.created_at DESC
        `);

        const tbody = document.getElementById('audit-plans-tbody');
        tbody.innerHTML = '';

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td>${row[2]}</td>
                    <td>${row[3]}</td>
                    <td>${row[4]}</td>
                    <td>${row[5] || '-'}</td>
                    <td><span class="status-badge status-${row[6].replace(' ', '-')}">${row[6]}</span></td>
                    <td class="table-actions">
                        <button class="btn btn-primary" onclick="viewAuditPlan(${row[0]})">عرض</button>
                        <button class="btn btn-success" onclick="updateAuditStatus(${row[0]}, 'قيد التنفيذ')">بدء</button>
                        <button class="btn btn-danger" onclick="deleteAuditPlan(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">لا توجد خطط مراجعة</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل خطط المراجعة:', error);
    }
}

function updateAuditStatus(planId, newStatus) {
    try {
        db.run("UPDATE audit_plans SET status = ? WHERE id = ?", [newStatus, planId]);
        showMessage(`تم تحديث حالة المراجعة إلى: ${newStatus}`, 'success');
        loadAuditPlans();
        updateDashboard();
    } catch (error) {
        console.error('خطأ في تحديث حالة المراجعة:', error);
        showMessage('خطأ في تحديث حالة المراجعة', 'error');
    }
}

function deleteAuditPlan(planId) {
    if (confirm('هل أنت متأكد من حذف خطة المراجعة؟ سيتم حذف جميع البيانات المرتبطة بها.')) {
        try {
            // حذف البيانات المرتبطة أولاً
            db.run("DELETE FROM audit_findings WHERE audit_plan_id = ?", [planId]);
            db.run("DELETE FROM audit_procedures WHERE audit_plan_id = ?", [planId]);
            db.run("DELETE FROM audit_plans WHERE id = ?", [planId]);

            showMessage('تم حذف خطة المراجعة بنجاح', 'success');
            loadAuditPlans();
            updateDashboard();
        } catch (error) {
            console.error('خطأ في حذف خطة المراجعة:', error);
            showMessage('خطأ في حذف خطة المراجعة', 'error');
        }
    }
}

// حفظ البيانات في التخزين المحلي
function saveToLocalStorage() {
    if (db) {
        const data = db.export();
        localStorage.setItem('auditSystemDB', JSON.stringify(Array.from(data)));
        showMessage('تم حفظ البيانات بنجاح', 'success');
    }
}

// استرداد البيانات من التخزين المحلي
function loadFromLocalStorage() {
    const savedData = localStorage.getItem('auditSystemDB');
    if (savedData) {
        try {
            const data = new Uint8Array(JSON.parse(savedData));
            db = new SQL.Database(data);
            updateDashboard();
            showMessage('تم استرداد البيانات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في استرداد البيانات:', error);
            showMessage('خطأ في استرداد البيانات', 'error');
        }
    }
}

// تصدير البيانات
function exportData() {
    if (db) {
        const data = db.export();
        const blob = new Blob([data], { type: 'application/octet-stream' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audit_system_backup_${new Date().toISOString().split('T')[0]}.db`;
        a.click();
        URL.revokeObjectURL(url);
        showMessage('تم تصدير البيانات بنجاح', 'success');
    }
}

// إدارة إجراءات المراجعة
function showAddProcedureForm() {
    document.getElementById('add-procedure-form').style.display = 'block';
    loadAuditPlansForSelect('procedure-audit-plan');
    loadAuditorsForSelect('procedure-assigned-auditor');
}

function hideAddProcedureForm() {
    document.getElementById('add-procedure-form').style.display = 'none';
    document.querySelector('#add-procedure-form form').reset();
}

function loadAuditPlansForSelect(selectId) {
    if (!db) return;

    try {
        const result = db.exec(`
            SELECT ap.id, c.name as client_name, ap.financial_year, ap.audit_type
            FROM audit_plans ap
            JOIN clients c ON ap.client_id = c.id
            WHERE ap.status != 'مكتمل'
            ORDER BY ap.created_at DESC
        `);

        const select = document.getElementById(selectId);

        // مسح الخيارات الحالية (عدا الخيار الافتراضي)
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const option = document.createElement('option');
                option.value = row[0];
                option.textContent = `${row[1]} - ${row[2]} (${row[3]})`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل خطط المراجعة:', error);
    }
}

function addProcedure(event) {
    event.preventDefault();

    const formData = {
        audit_plan_id: document.getElementById('procedure-audit-plan').value,
        procedure_name: document.getElementById('procedure-name').value,
        description: document.getElementById('procedure-description').value,
        assigned_auditor_id: document.getElementById('procedure-assigned-auditor').value,
        start_date: document.getElementById('procedure-start-date').value
    };

    try {
        const stmt = db.prepare(`
            INSERT INTO audit_procedures (audit_plan_id, procedure_name, description, assigned_auditor_id, start_date)
            VALUES (?, ?, ?, ?, ?)
        `);

        stmt.run([
            formData.audit_plan_id,
            formData.procedure_name,
            formData.description,
            formData.assigned_auditor_id || null,
            formData.start_date || null
        ]);

        stmt.free();

        showMessage('تم إضافة الإجراء بنجاح', 'success');
        hideAddProcedureForm();
        loadProcedures();

    } catch (error) {
        console.error('خطأ في إضافة الإجراء:', error);
        showMessage('خطأ في إضافة الإجراء', 'error');
    }
}

function loadProcedures() {
    if (!db) return;

    try {
        const result = db.exec(`
            SELECT pr.id, pr.procedure_name, c.name as client_name,
                   a.name as auditor_name, pr.start_date, pr.status
            FROM audit_procedures pr
            JOIN audit_plans ap ON pr.audit_plan_id = ap.id
            JOIN clients c ON ap.client_id = c.id
            LEFT JOIN auditors a ON pr.assigned_auditor_id = a.id
            ORDER BY pr.created_at DESC
        `);

        const tbody = document.getElementById('procedures-tbody');
        tbody.innerHTML = '';

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td>${row[2]}</td>
                    <td>${row[3] || 'غير محدد'}</td>
                    <td>${row[4] || '-'}</td>
                    <td><span class="status-badge status-${row[5].replace(' ', '-')}">${row[5]}</span></td>
                    <td>
                        <div class="progress">
                            <div class="progress-bar" style="width: ${getProgressPercentage(row[5])}%">
                                ${getProgressPercentage(row[5])}%
                            </div>
                        </div>
                    </td>
                    <td class="table-actions">
                        <button class="btn btn-success" onclick="updateProcedureStatus(${row[0]}, 'قيد التنفيذ')">بدء</button>
                        <button class="btn btn-primary" onclick="updateProcedureStatus(${row[0]}, 'مكتمل')">إكمال</button>
                        <button class="btn btn-danger" onclick="deleteProcedure(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">لا توجد إجراءات مراجعة</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل الإجراءات:', error);
    }
}

function getProgressPercentage(status) {
    switch(status) {
        case 'لم يبدأ': return 0;
        case 'قيد التنفيذ': return 50;
        case 'مكتمل': return 100;
        default: return 0;
    }
}

function updateProcedureStatus(procedureId, newStatus) {
    try {
        const updateData = [newStatus, procedureId];
        if (newStatus === 'مكتمل') {
            db.run("UPDATE audit_procedures SET status = ?, completion_date = CURRENT_TIMESTAMP WHERE id = ?", updateData);
        } else {
            db.run("UPDATE audit_procedures SET status = ? WHERE id = ?", updateData);
        }

        showMessage(`تم تحديث حالة الإجراء إلى: ${newStatus}`, 'success');
        loadProcedures();
    } catch (error) {
        console.error('خطأ في تحديث حالة الإجراء:', error);
        showMessage('خطأ في تحديث حالة الإجراء', 'error');
    }
}

function deleteProcedure(procedureId) {
    if (confirm('هل أنت متأكد من حذف هذا الإجراء؟')) {
        try {
            db.run("DELETE FROM audit_procedures WHERE id = ?", [procedureId]);
            showMessage('تم حذف الإجراء بنجاح', 'success');
            loadProcedures();
        } catch (error) {
            console.error('خطأ في حذف الإجراء:', error);
            showMessage('خطأ في حذف الإجراء', 'error');
        }
    }
}

// إدارة النتائج والملاحظات
function showAddFindingForm() {
    document.getElementById('add-finding-form').style.display = 'block';
    loadAuditPlansForSelect('finding-audit-plan');
}

function hideAddFindingForm() {
    document.getElementById('add-finding-form').style.display = 'none';
    document.querySelector('#add-finding-form form').reset();
}

function addFinding(event) {
    event.preventDefault();

    const formData = {
        audit_plan_id: document.getElementById('finding-audit-plan').value,
        finding_type: document.getElementById('finding-type').value,
        severity: document.getElementById('finding-severity').value,
        description: document.getElementById('finding-description').value,
        recommendation: document.getElementById('finding-recommendation').value,
        due_date: document.getElementById('finding-due-date').value
    };

    try {
        const stmt = db.prepare(`
            INSERT INTO audit_findings (audit_plan_id, finding_type, severity, description, recommendation, due_date)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        stmt.run([
            formData.audit_plan_id,
            formData.finding_type,
            formData.severity,
            formData.description,
            formData.recommendation,
            formData.due_date || null
        ]);

        stmt.free();

        showMessage('تم إضافة الملاحظة بنجاح', 'success');
        hideAddFindingForm();
        loadFindings();
        updateDashboard();

    } catch (error) {
        console.error('خطأ في إضافة الملاحظة:', error);
        showMessage('خطأ في إضافة الملاحظة', 'error');
    }
}

function loadFindings() {
    if (!db) return;

    try {
        const result = db.exec(`
            SELECT af.id, af.finding_type, af.severity, af.description,
                   c.name as client_name, af.due_date, af.status
            FROM audit_findings af
            JOIN audit_plans ap ON af.audit_plan_id = ap.id
            JOIN clients c ON ap.client_id = c.id
            ORDER BY af.created_at DESC
        `);

        const tbody = document.getElementById('findings-tbody');
        tbody.innerHTML = '';

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td><span class="status-badge status-${row[2]}">${row[2]}</span></td>
                    <td>${row[3].substring(0, 100)}${row[3].length > 100 ? '...' : ''}</td>
                    <td>${row[4]}</td>
                    <td>${row[5] || '-'}</td>
                    <td><span class="status-badge status-${row[6].replace(' ', '-')}">${row[6]}</span></td>
                    <td class="table-actions">
                        <button class="btn btn-primary" onclick="viewFinding(${row[0]})">عرض</button>
                        <button class="btn btn-success" onclick="updateFindingStatus(${row[0]}, 'مغلق')">إغلاق</button>
                        <button class="btn btn-danger" onclick="deleteFinding(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">لا توجد ملاحظات</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل الملاحظات:', error);
    }
}

function updateFindingStatus(findingId, newStatus) {
    try {
        db.run("UPDATE audit_findings SET status = ? WHERE id = ?", [newStatus, findingId]);
        showMessage(`تم تحديث حالة الملاحظة إلى: ${newStatus}`, 'success');
        loadFindings();
        updateDashboard();
    } catch (error) {
        console.error('خطأ في تحديث حالة الملاحظة:', error);
        showMessage('خطأ في تحديث حالة الملاحظة', 'error');
    }
}

function deleteFinding(findingId) {
    if (confirm('هل أنت متأكد من حذف هذه الملاحظة؟')) {
        try {
            db.run("DELETE FROM audit_findings WHERE id = ?", [findingId]);
            showMessage('تم حذف الملاحظة بنجاح', 'success');
            loadFindings();
            updateDashboard();
        } catch (error) {
            console.error('خطأ في حذف الملاحظة:', error);
            showMessage('خطأ في حذف الملاحظة', 'error');
        }
    }
}

// إدارة المخاطر
function showAddRiskForm() {
    document.getElementById('add-risk-form').style.display = 'block';
    loadClientsForSelect('risk-client');
}

function hideAddRiskForm() {
    document.getElementById('add-risk-form').style.display = 'none';
    document.querySelector('#add-risk-form form').reset();
}

function loadClientsForSelect(selectId) {
    if (!db) return;

    try {
        const result = db.exec("SELECT id, name FROM clients ORDER BY name");
        const select = document.getElementById(selectId);

        // مسح الخيارات الحالية (عدا الخيار الافتراضي)
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const option = document.createElement('option');
                option.value = row[0];
                option.textContent = row[1];
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
    }
}

function addRisk(event) {
    event.preventDefault();

    const formData = {
        client_id: document.getElementById('risk-client').value,
        risk_category: document.getElementById('risk-category').value,
        risk_description: document.getElementById('risk-description').value,
        likelihood: document.getElementById('risk-likelihood').value,
        impact: document.getElementById('risk-impact').value,
        mitigation_strategy: document.getElementById('risk-mitigation').value
    };

    // حساب مستوى المخاطرة
    const riskLevel = calculateRiskLevel(formData.likelihood, formData.impact);

    try {
        const stmt = db.prepare(`
            INSERT INTO risks (client_id, risk_category, risk_description, likelihood, impact, risk_level, mitigation_strategy)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run([
            formData.client_id,
            formData.risk_category,
            formData.risk_description,
            formData.likelihood,
            formData.impact,
            riskLevel,
            formData.mitigation_strategy
        ]);

        stmt.free();

        showMessage('تم إضافة المخاطرة بنجاح', 'success');
        hideAddRiskForm();
        loadRisks();

    } catch (error) {
        console.error('خطأ في إضافة المخاطرة:', error);
        showMessage('خطأ في إضافة المخاطرة', 'error');
    }
}

function calculateRiskLevel(likelihood, impact) {
    const riskMatrix = {
        'عالي': { 'عالي': 'عالي جداً', 'متوسط': 'عالي', 'منخفض': 'متوسط' },
        'متوسط': { 'عالي': 'عالي', 'متوسط': 'متوسط', 'منخفض': 'منخفض' },
        'منخفض': { 'عالي': 'متوسط', 'متوسط': 'منخفض', 'منخفض': 'منخفض جداً' }
    };

    return riskMatrix[likelihood][impact] || 'غير محدد';
}

function loadRisks() {
    if (!db) return;

    try {
        const result = db.exec(`
            SELECT r.id, c.name as client_name, r.risk_category, r.risk_description,
                   r.likelihood, r.impact, r.risk_level, r.status
            FROM risks r
            JOIN clients c ON r.client_id = c.id
            ORDER BY r.created_at DESC
        `);

        const tbody = document.getElementById('risks-tbody');
        tbody.innerHTML = '';

        if (result.length > 0) {
            const rows = result[0].values;
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[1]}</td>
                    <td>${row[2]}</td>
                    <td>${row[3].substring(0, 100)}${row[3].length > 100 ? '...' : ''}</td>
                    <td><span class="status-badge status-${row[4]}">${row[4]}</span></td>
                    <td><span class="status-badge status-${row[5]}">${row[5]}</span></td>
                    <td><span class="status-badge status-${row[6].replace(' ', '-')}">${row[6]}</span></td>
                    <td><span class="status-badge status-${row[7].replace(' ', '-')}">${row[7]}</span></td>
                    <td class="table-actions">
                        <button class="btn btn-primary" onclick="viewRisk(${row[0]})">عرض</button>
                        <button class="btn btn-success" onclick="updateRiskStatus(${row[0]}, 'مخفف')">تخفيف</button>
                        <button class="btn btn-danger" onclick="deleteRisk(${row[0]})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="8" class="empty-state">لا توجد مخاطر مسجلة</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل المخاطر:', error);
    }
}

function updateRiskStatus(riskId, newStatus) {
    try {
        db.run("UPDATE risks SET status = ? WHERE id = ?", [newStatus, riskId]);
        showMessage(`تم تحديث حالة المخاطرة إلى: ${newStatus}`, 'success');
        loadRisks();
    } catch (error) {
        console.error('خطأ في تحديث حالة المخاطرة:', error);
        showMessage('خطأ في تحديث حالة المخاطرة', 'error');
    }
}

function deleteRisk(riskId) {
    if (confirm('هل أنت متأكد من حذف هذه المخاطرة؟')) {
        try {
            db.run("DELETE FROM risks WHERE id = ?", [riskId]);
            showMessage('تم حذف المخاطرة بنجاح', 'success');
            loadRisks();
        } catch (error) {
            console.error('خطأ في حذف المخاطرة:', error);
            showMessage('خطأ في حذف المخاطرة', 'error');
        }
    }
}

// إدارة التقارير
function generateComprehensiveReport() {
    if (!db) return;

    try {
        const reportContent = document.getElementById('report-content');
        const reportBody = document.getElementById('report-body');

        let html = '<h3>تقرير المراجعة الشامل</h3>';
        html += `<p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>`;

        // إحصائيات عامة
        const clientsResult = db.exec("SELECT COUNT(*) FROM clients");
        const auditorsResult = db.exec("SELECT COUNT(*) FROM auditors");
        const auditsResult = db.exec("SELECT COUNT(*) FROM audit_plans");
        const findingsResult = db.exec("SELECT COUNT(*) FROM audit_findings WHERE status = 'مفتوح'");

        html += '<h4>الإحصائيات العامة</h4>';
        html += '<table class="report-table">';
        html += `<tr><td>إجمالي العملاء</td><td>${clientsResult[0]?.values[0][0] || 0}</td></tr>`;
        html += `<tr><td>إجمالي المراجعين</td><td>${auditorsResult[0]?.values[0][0] || 0}</td></tr>`;
        html += `<tr><td>إجمالي المراجعات</td><td>${auditsResult[0]?.values[0][0] || 0}</td></tr>`;
        html += `<tr><td>الملاحظات المفتوحة</td><td>${findingsResult[0]?.values[0][0] || 0}</td></tr>`;
        html += '</table>';

        // المراجعات النشطة
        const activeAuditsResult = db.exec(`
            SELECT c.name, ap.audit_type, ap.status, a.name as auditor_name
            FROM audit_plans ap
            JOIN clients c ON ap.client_id = c.id
            JOIN auditors a ON ap.lead_auditor_id = a.id
            WHERE ap.status IN ('مخطط', 'قيد التنفيذ')
        `);

        html += '<h4>المراجعات النشطة</h4>';
        if (activeAuditsResult.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>العميل</th><th>نوع المراجعة</th><th>الحالة</th><th>المراجع الرئيسي</th></tr>';
            activeAuditsResult[0].values.forEach(row => {
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2]}</td><td>${row[3]}</td></tr>`;
            });
            html += '</table>';
        } else {
            html += '<p>لا توجد مراجعات نشطة حالياً</p>';
        }

        reportBody.innerHTML = html;
        reportContent.style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showMessage('خطأ في إنشاء التقرير', 'error');
    }
}

function generateFindingsReport() {
    if (!db) return;

    try {
        const reportContent = document.getElementById('report-content');
        const reportBody = document.getElementById('report-body');

        let html = '<h3>تقرير الملاحظات والنتائج</h3>';
        html += `<p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>`;

        // الملاحظات حسب مستوى الخطورة
        const findingsBySeverity = db.exec(`
            SELECT severity, COUNT(*) as count
            FROM audit_findings
            GROUP BY severity
        `);

        html += '<h4>الملاحظات حسب مستوى الخطورة</h4>';
        if (findingsBySeverity.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>مستوى الخطورة</th><th>العدد</th></tr>';
            findingsBySeverity[0].values.forEach(row => {
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td></tr>`;
            });
            html += '</table>';
        }

        // الملاحظات المفتوحة
        const openFindings = db.exec(`
            SELECT af.finding_type, af.severity, af.description, c.name as client_name
            FROM audit_findings af
            JOIN audit_plans ap ON af.audit_plan_id = ap.id
            JOIN clients c ON ap.client_id = c.id
            WHERE af.status = 'مفتوح'
            ORDER BY af.severity DESC
        `);

        html += '<h4>الملاحظات المفتوحة</h4>';
        if (openFindings.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>النوع</th><th>الخطورة</th><th>الوصف</th><th>العميل</th></tr>';
            openFindings[0].values.forEach(row => {
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2].substring(0, 100)}...</td><td>${row[3]}</td></tr>`;
            });
            html += '</table>';
        } else {
            html += '<p>لا توجد ملاحظات مفتوحة</p>';
        }

        reportBody.innerHTML = html;
        reportContent.style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء تقرير الملاحظات:', error);
        showMessage('خطأ في إنشاء تقرير الملاحظات', 'error');
    }
}

function generatePerformanceReport() {
    if (!db) return;

    try {
        const reportContent = document.getElementById('report-content');
        const reportBody = document.getElementById('report-body');

        let html = '<h3>تقرير الأداء</h3>';
        html += `<p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>`;

        // أداء المراجعين
        const auditorPerformance = db.exec(`
            SELECT a.name, COUNT(ap.id) as audit_count,
                   COUNT(CASE WHEN ap.status = 'مكتمل' THEN 1 END) as completed_count
            FROM auditors a
            LEFT JOIN audit_plans ap ON a.id = ap.lead_auditor_id
            GROUP BY a.id, a.name
            ORDER BY audit_count DESC
        `);

        html += '<h4>أداء المراجعين</h4>';
        if (auditorPerformance.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>المراجع</th><th>إجمالي المراجعات</th><th>المراجعات المكتملة</th><th>معدل الإنجاز</th></tr>';
            auditorPerformance[0].values.forEach(row => {
                const completionRate = row[1] > 0 ? ((row[2] / row[1]) * 100).toFixed(1) : 0;
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2]}</td><td>${completionRate}%</td></tr>`;
            });
            html += '</table>';
        }

        reportBody.innerHTML = html;
        reportContent.style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء تقرير الأداء:', error);
        showMessage('خطأ في إنشاء تقرير الأداء', 'error');
    }
}

function generateRiskReport() {
    if (!db) return;

    try {
        const reportContent = document.getElementById('report-content');
        const reportBody = document.getElementById('report-body');

        let html = '<h3>تقرير المخاطر</h3>';
        html += `<p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>`;

        // المخاطر حسب المستوى
        const risksByLevel = db.exec(`
            SELECT risk_level, COUNT(*) as count
            FROM risks
            WHERE status = 'نشط'
            GROUP BY risk_level
            ORDER BY count DESC
        `);

        html += '<h4>المخاطر النشطة حسب المستوى</h4>';
        if (risksByLevel.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>مستوى المخاطرة</th><th>العدد</th></tr>';
            risksByLevel[0].values.forEach(row => {
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td></tr>`;
            });
            html += '</table>';
        }

        // المخاطر عالية الأولوية
        const highRisks = db.exec(`
            SELECT c.name as client_name, r.risk_category, r.risk_description, r.risk_level
            FROM risks r
            JOIN clients c ON r.client_id = c.id
            WHERE r.risk_level IN ('عالي جداً', 'عالي') AND r.status = 'نشط'
            ORDER BY r.risk_level DESC
        `);

        html += '<h4>المخاطر عالية الأولوية</h4>';
        if (highRisks.length > 0) {
            html += '<table class="report-table">';
            html += '<tr><th>العميل</th><th>فئة المخاطرة</th><th>الوصف</th><th>المستوى</th></tr>';
            highRisks[0].values.forEach(row => {
                html += `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2].substring(0, 100)}...</td><td>${row[3]}</td></tr>`;
            });
            html += '</table>';
        } else {
            html += '<p>لا توجد مخاطر عالية الأولوية</p>';
        }

        reportBody.innerHTML = html;
        reportContent.style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء تقرير المخاطر:', error);
        showMessage('خطأ في إنشاء تقرير المخاطر', 'error');
    }
}

function printReport() {
    window.print();
}

function exportReportPDF() {
    showMessage('ميزة تصدير PDF قيد التطوير', 'warning');
}

// إضافة بيانات تجريبية
function addSampleData() {
    if (!db) return;

    try {
        // إضافة عملاء تجريبيين
        const sampleClients = [
            ['شركة النور للتجارة', 'شركة محدودة', 'التجارة', 'أحمد محمد', '0501234567', '<EMAIL>', 'الرياض، المملكة العربية السعودية'],
            ['مؤسسة الفجر للمقاولات', 'مؤسسة فردية', 'المقاولات', 'فاطمة علي', '0509876543', '<EMAIL>', 'جدة، المملكة العربية السعودية'],
            ['جمعية الخير الخيرية', 'جمعية', 'العمل الخيري', 'محمد سالم', '0551234567', '<EMAIL>', 'الدمام، المملكة العربية السعودية']
        ];

        const clientStmt = db.prepare(`
            INSERT INTO clients (name, type, industry, contact_person, phone, email, address)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        sampleClients.forEach(client => {
            clientStmt.run(client);
        });
        clientStmt.free();

        // إضافة مراجعين تجريبيين
        const sampleAuditors = [
            ['د. عبدالله الأحمد', 'شريك', 'CPA-001', 'مراجعة مالية', 15, '<EMAIL>', '**********'],
            ['أ. سارة المحمد', 'مدير', 'CPA-002', 'مراجعة داخلية', 10, '<EMAIL>', '**********'],
            ['م. خالد العلي', 'مراجع أول', 'CPA-003', 'مراجعة نظم', 8, '<EMAIL>', '**********'],
            ['أ. نورا السالم', 'مراجع', 'CPA-004', 'مراجعة امتثال', 5, '<EMAIL>', '**********']
        ];

        const auditorStmt = db.prepare(`
            INSERT INTO auditors (name, position, license_number, specialization, experience_years, email, phone)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        sampleAuditors.forEach(auditor => {
            auditorStmt.run(auditor);
        });
        auditorStmt.free();

        // إضافة خطة مراجعة تجريبية
        const auditPlanStmt = db.prepare(`
            INSERT INTO audit_plans (client_id, financial_year, audit_type, lead_auditor_id, start_date, end_date, materiality_level, objectives, scope, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        auditPlanStmt.run([
            1, // client_id
            2024, // financial_year
            'مراجعة سنوية', // audit_type
            1, // lead_auditor_id
            '2024-01-15', // start_date
            '2024-03-15', // end_date
            50000, // materiality_level
            'مراجعة القوائم المالية للسنة المنتهية في 31 ديسمبر 2023 وإبداء الرأي حول عدالة عرضها', // objectives
            'مراجعة شاملة للقوائم المالية والأنظمة المحاسبية والرقابة الداخلية', // scope
            'قيد التنفيذ' // status
        ]);
        auditPlanStmt.free();

        // إضافة إجراءات مراجعة تجريبية
        const procedureStmt = db.prepare(`
            INSERT INTO audit_procedures (audit_plan_id, procedure_name, description, assigned_auditor_id, status, start_date)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        const sampleProcedures = [
            [1, 'مراجعة النقدية', 'التحقق من أرصدة النقدية والبنوك', 2, 'مكتمل', '2024-01-15'],
            [1, 'مراجعة المدينين', 'التحقق من أرصدة العملاء والمدينين', 3, 'قيد التنفيذ', '2024-01-20'],
            [1, 'مراجعة المخزون', 'جرد وتقييم المخزون', 4, 'لم يبدأ', null]
        ];

        sampleProcedures.forEach(procedure => {
            procedureStmt.run(procedure);
        });
        procedureStmt.free();

        // إضافة ملاحظات تجريبية
        const findingStmt = db.prepare(`
            INSERT INTO audit_findings (audit_plan_id, finding_type, severity, description, recommendation, status, due_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        const sampleFindings = [
            [1, 'نقص في الرقابة الداخلية', 'متوسط', 'عدم وجود فصل كافي في الواجبات بين أمين الصندوق ومسؤول المحاسبة', 'تطبيق مبدأ الفصل بين الواجبات وتعيين مسؤوليات واضحة', 'مفتوح', '2024-04-15'],
            [1, 'خطأ محاسبي', 'منخفض', 'خطأ في تصنيف بعض المصروفات', 'إعادة تصنيف المصروفات وتدريب الموظفين', 'مفتوح', '2024-03-30']
        ];

        sampleFindings.forEach(finding => {
            findingStmt.run(finding);
        });
        findingStmt.free();

        // إضافة مخاطر تجريبية
        const riskStmt = db.prepare(`
            INSERT INTO risks (client_id, risk_category, risk_description, likelihood, impact, risk_level, mitigation_strategy, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const sampleRisks = [
            [1, 'مخاطر تشغيلية', 'اعتماد على موظف واحد في العمليات المحاسبية الرئيسية', 'متوسط', 'عالي', 'عالي', 'تدريب موظفين إضافيين وتوثيق الإجراءات', 'نشط'],
            [2, 'مخاطر مالية', 'تركز العملاء في قطاع واحد', 'عالي', 'متوسط', 'عالي', 'تنويع قاعدة العملاء', 'نشط']
        ];

        sampleRisks.forEach(risk => {
            riskStmt.run(risk);
        });
        riskStmt.free();

        showMessage('تم إضافة البيانات التجريبية بنجاح', 'success');
        updateDashboard();

    } catch (error) {
        console.error('خطأ في إضافة البيانات التجريبية:', error);
        showMessage('خطأ في إضافة البيانات التجريبية', 'error');
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة قاعدة البيانات
    initDatabase();

    // إعداد التنقل
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('href').substring(1);
            showSection(sectionId);
        });
    });

    // إظهار لوحة التحكم افتراضياً
    showSection('dashboard');

    // إضافة البيانات التجريبية بعد ثانيتين من التحميل
    setTimeout(() => {
        const hasData = localStorage.getItem('auditSystemDB');
        if (!hasData) {
            addSampleData();
        }
    }, 2000);

    // حفظ البيانات تلقائياً كل 5 دقائق
    setInterval(saveToLocalStorage, 300000);
});
